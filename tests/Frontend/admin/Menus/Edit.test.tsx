import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import Edit from '@/pages/admin/Menus/Edit';

// Mock Inertia
const mockPut = vi.fn();
const mockSetData = vi.fn();

vi.mock('@inertiajs/react', () => ({
    Head: ({ children }: { children: React.ReactNode }) => <>{children}</>,
    Link: ({ children, href, ...props }: any) => <a href={href} {...props}>{children}</a>,
    useForm: vi.fn(() => ({
        data: {
            name: 'Test Menu',
            location: 'header',
            description: 'Test Description',
            is_active: true,
        },
        setData: mockSetData,
        put: mockPut,
        processing: false,
        errors: {},
    })),
    router: {
        delete: vi.fn(),
    },
}));

// <PERSON>ck delete confirmation hook
const mockConfirmDelete = vi.fn();
vi.mock('@/hooks/use-delete-confirmation', () => ({
    useDeleteConfirmation: () => ({
        confirmDelete: mockConfirmDelete,
        showDeleteConfirmation: mockConfirmDelete,
    }),
}));

// Mock toast
vi.mock('sonner', () => ({
    toast: {
        success: vi.fn(),
        error: vi.fn(),
    },
}));

// Mock AppLayout
vi.mock('@/layouts/app-layout', () => ({
    default: ({ children }: { children: React.ReactNode }) => <div data-testid="app-layout">{children}</div>,
}));

// Mock UI components
vi.mock('@/components/ui/card', () => ({
    Card: ({ children, ...props }: any) => <div data-testid="card" {...props}>{children}</div>,
    CardContent: ({ children, ...props }: any) => <div data-testid="card-content" {...props}>{children}</div>,
    CardDescription: ({ children, ...props }: any) => <div data-testid="card-description" {...props}>{children}</div>,
    CardHeader: ({ children, ...props }: any) => <div data-testid="card-header" {...props}>{children}</div>,
    CardTitle: ({ children, ...props }: any) => <div data-testid="card-title" {...props}>{children}</div>,
}));

vi.mock('@/components/ui/button', () => ({
    Button: ({ children, onClick, type, variant, asChild, ...props }: any) => {
        // Filter out asChild prop to avoid React warnings
        const { asChild: _, ...buttonProps } = props;
        return (
            <button onClick={onClick} type={type} data-testid={`button-${variant || 'default'}`} {...buttonProps}>
                {children}
            </button>
        );
    },
}));

vi.mock('@/components/ui/input', () => ({
    Input: ({ onChange, value, ...props }: any) => (
        <input 
            onChange={onChange} 
            value={value} 
            data-testid="input" 
            {...props} 
        />
    ),
}));

vi.mock('@/components/ui/label', () => ({
    Label: ({ children, ...props }: any) => <label data-testid="label" {...props}>{children}</label>,
}));

vi.mock('@/components/ui/textarea', () => ({
    Textarea: ({ onChange, value, ...props }: any) => (
        <textarea 
            onChange={onChange} 
            value={value} 
            data-testid="textarea" 
            {...props} 
        />
    ),
}));

vi.mock('@/components/ui/select', () => ({
    Select: ({ children, onValueChange, ...props }: any) => (
        <div data-testid="select" {...props}>
            {children}
        </div>
    ),
    SelectContent: ({ children, ...props }: any) => <div data-testid="select-content" {...props}>{children}</div>,
    SelectItem: ({ children, value, ...props }: any) => (
        <option value={value} data-testid="select-item" {...props}>
            {children}
        </option>
    ),
    SelectTrigger: ({ children, ...props }: any) => <div data-testid="select-trigger" {...props}>{children}</div>,
    SelectValue: ({ placeholder, ...props }: any) => <span data-testid="select-value" {...props}>{placeholder}</span>,
}));

vi.mock('@/components/ui/switch', () => ({
    Switch: ({ checked, onCheckedChange, ...props }: any) => (
        <input
            type="checkbox"
            checked={checked}
            onChange={(e) => onCheckedChange && onCheckedChange(e.target.checked)}
            data-testid="switch"
            {...props}
        />
    ),
}));

describe('Menu Edit Component', () => {
    const mockMenu = {
        id: 1,
        name: 'Test Menu',
        location: 'header',
        description: 'Test Description',
        is_active: true,
        created_at: '2024-01-01T00:00:00.000000Z',
        updated_at: '2024-01-01T00:00:00.000000Z',
    };

    const mockProps = {
        menu: mockMenu,
        locations: {
            header: 'Header',
            footer: 'Footer',
            sidebar: 'Sidebar',
        },
    };

    beforeEach(() => {
        vi.clearAllMocks();
    });

    it('renders the edit menu form with existing data', () => {
        render(<Edit {...mockProps} />);
        
        expect(screen.getByText('Edit Menu')).toBeInTheDocument();
        expect(screen.getByDisplayValue('Test Menu')).toBeInTheDocument();
        expect(screen.getByDisplayValue('Test Description')).toBeInTheDocument();
        expect(screen.getByTestId('app-layout')).toBeInTheDocument();
    });

    it('handles form input changes', () => {
        render(<Edit {...mockProps} />);
        
        const nameInput = screen.getByDisplayValue('Test Menu');
        fireEvent.change(nameInput, { target: { value: 'Updated Menu' } });
        
        expect(mockSetData).toHaveBeenCalledWith('name', 'Updated Menu');
    });

    it('handles switch toggle', () => {
        render(<Edit {...mockProps} />);
        
        const switchElement = screen.getByTestId('switch');
        fireEvent.click(switchElement);
        
        expect(mockSetData).toHaveBeenCalledWith('is_active', expect.any(Boolean));
    });

    it('submits form with correct data', async () => {
        render(<Edit {...mockProps} />);

        const form = screen.getByTestId('menu-form') || document.getElementById('menu-form');
        fireEvent.submit(form!);
        
        await waitFor(() => {
            expect(mockPut).toHaveBeenCalledWith(`/admin/menus/${mockMenu.id}`, expect.objectContaining({
                onSuccess: expect.any(Function),
                onError: expect.any(Function),
            }));
        });
    });

    it('handles delete confirmation', () => {
        render(<Edit {...mockProps} />);
        
        const deleteButton = screen.getByTestId('button-destructive');
        fireEvent.click(deleteButton);
        
        expect(mockConfirmDelete).toHaveBeenCalledWith(expect.objectContaining({
            title: expect.stringContaining('Delete Menu'),
            description: expect.any(String),
            onConfirm: expect.any(Function),
        }));
    });

    it('displays validation errors when present', () => {
        const propsWithErrors = {
            ...mockProps,
            errors: {
                name: 'Name is required',
                location: 'Location is required',
            },
        };

        render(<Edit {...propsWithErrors} />);

        // Check if the form renders correctly with error props
        expect(screen.getByDisplayValue('Test Menu')).toBeInTheDocument(); // Name field should show the mocked value
        // Note: Error display depends on how the component handles errors
        // This test verifies the component renders with error props
    });

    it('shows processing state during form submission', () => {
        // Mock the useForm hook to return processing state
        const mockUseFormProcessing = vi.fn(() => ({
            data: {
                name: 'Test Menu',
                location: 'header',
                description: 'Test Description',
                is_active: true,
            },
            setData: mockSetData,
            put: mockPut,
            processing: true,
            errors: {},
        }));

        // Override the mock temporarily
        mockUseForm.mockImplementation(mockUseFormProcessing);

        render(<Edit {...mockProps} />);

        const submitButton = screen.getByRole('button', { name: /update menu/i });
        expect(submitButton).toBeDisabled();
    });
});
