import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { router } from '@inertiajs/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import Index from '@/pages/admin/Pages/Index';
import Create from '@/pages/admin/Pages/Create';
import Edit from '@/pages/admin/Pages/Edit';

// Mock Inertia router
vi.mock('@inertiajs/react', () => ({
    router: {
        get: vi.fn(),
        post: vi.fn(),
        put: vi.fn(),
        delete: vi.fn(),
    },
    usePage: () => ({
        props: {
            auth: {
                user: {
                    id: 1,
                    name: 'Test Admin',
                    email: '<EMAIL>',
                    isAdmin: () => true,
                }
            }
        },
        url: '/admin/pages'
    }),
    Head: ({ children }: { children: React.ReactNode }) => <>{children}</>,
    Link: ({ children, href, ...props }: any) => <a href={href} {...props}>{children}</a>,
    useForm: vi.fn(() => ({
        data: {
            title: '',
            slug: '',
            content: '',
            featured_image: '',
            meta_description: '',
            meta_keywords: '',
            layout: 'default',
            is_published: false,
            published_at: '',
        },
        setData: vi.fn(),
        post: vi.fn(),
        put: vi.fn(),
        processing: false,
        errors: {},
        reset: vi.fn(),
    })),
}));

// Mock toast notifications
vi.mock('sonner', () => ({
    toast: {
        success: vi.fn(),
        error: vi.fn(),
    },
    Toaster: ({ children }: { children?: React.ReactNode }) => <div data-testid="toaster">{children}</div>,
}));

// Mock delete confirmation hook
vi.mock('@/hooks/use-delete-confirmation', () => ({
    useDeleteConfirmation: () => ({
        showDeleteConfirmation: vi.fn((options) => {
            // Simulate immediate confirmation for testing
            options.onConfirm();
        }),
    }),
}));

describe('Page Management Admin Interface', () => {
    const mockPages = {
        data: [
            {
                id: 1,
                title: 'Test Page',
                slug: 'test-page',
                content: '<p>Test content</p>',
                featured_image: null,
                meta_description: 'Test description',
                meta_keywords: 'test, page',
                layout: 'default',
                is_published: true,
                author_id: 1,
                published_at: '2025-01-15T10:00:00.000000Z',
                created_at: '2025-01-15T10:00:00.000000Z',
                updated_at: '2025-01-15T10:00:00.000000Z',
                author: {
                    id: 1,
                    name: 'Test Author',
                    email: '<EMAIL>',
                },
                url: 'http://localhost/page/test-page',
                excerpt: 'Test description',
            },
        ],
        current_page: 1,
        last_page: 1,
        per_page: 15,
        total: 1,
        from: 1,
        to: 1,
    };

    const mockFilters = {
        search: '',
        status: 'all',
        layout: 'all',
    };

    const mockLayouts = {
        default: 'Default Layout',
        'full-width': 'Full Width',
        landing: 'Landing Page',
    };

    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe('Pages Index', () => {
        it('renders pages list correctly', () => {
            render(
                <Index 
                    pages={mockPages} 
                    filters={mockFilters} 
                    layouts={mockLayouts} 
                />
            );

            expect(screen.getByText('Page Management')).toBeInTheDocument();
            expect(screen.getByText('Test Page')).toBeInTheDocument();
            expect(screen.getByText('test-page')).toBeInTheDocument();
            // Use getAllByText for Published since it appears in both header and badge
            expect(screen.getAllByText('Published')).toHaveLength(2);
            expect(screen.getByText('Default Layout')).toBeInTheDocument();
        });

        it('displays view button with correct URL', () => {
            render(
                <Index 
                    pages={mockPages} 
                    filters={mockFilters} 
                    layouts={mockLayouts} 
                />
            );

            const viewButton = screen.getByRole('link', { name: /view/i });
            expect(viewButton).toHaveAttribute('href', 'http://localhost/page/test-page');
            expect(viewButton).toHaveAttribute('target', '_blank');
        });

        it('displays edit button with correct URL', () => {
            render(
                <Index 
                    pages={mockPages} 
                    filters={mockFilters} 
                    layouts={mockLayouts} 
                />
            );

            const editButton = screen.getByRole('link', { name: /edit/i });
            expect(editButton).toHaveAttribute('href', '/admin/pages/1/edit');
        });

        it('handles delete button click', async () => {
            render(
                <Index 
                    pages={mockPages} 
                    filters={mockFilters} 
                    layouts={mockLayouts} 
                />
            );

            const deleteButton = screen.getByRole('button', { name: /delete/i });
            fireEvent.click(deleteButton);

            await waitFor(() => {
                expect(router.delete).toHaveBeenCalledWith('/admin/pages/1', expect.any(Object));
            });
        });

        it('handles search functionality', () => {
            render(
                <Index 
                    pages={mockPages} 
                    filters={mockFilters} 
                    layouts={mockLayouts} 
                />
            );

            const searchInput = screen.getByPlaceholderText('Search pages...');
            fireEvent.change(searchInput, { target: { value: 'test search' } });

            expect(searchInput).toHaveValue('test search');
        });

        it('handles status filter changes', () => {
            render(
                <Index 
                    pages={mockPages} 
                    filters={mockFilters} 
                    layouts={mockLayouts} 
                />
            );

            // The status filter should be present
            expect(screen.getByText('All Status')).toBeInTheDocument();
        });
    });

    describe('Pages Create', () => {
        it('renders create form correctly', () => {
            render(<Create layouts={mockLayouts} />);

            // Use getAllByText for Create Page since it appears in both header and button
            expect(screen.getAllByText('Create Page')).toHaveLength(2);
            expect(screen.getByLabelText(/title/i)).toBeInTheDocument();
            expect(screen.getByLabelText(/url slug/i)).toBeInTheDocument();
            expect(screen.getByLabelText(/content/i)).toBeInTheDocument();
        });

        it('auto-generates slug from title', () => {
            // This test verifies that the slug generation logic exists in the component
            // Since we're mocking useForm, we can't test the actual slug generation
            // but we can verify the form elements are present and the component renders
            render(<Create layouts={mockLayouts} />);

            const titleInput = screen.getByLabelText(/title/i);
            const slugInput = screen.getByLabelText(/url slug/i);

            expect(titleInput).toBeInTheDocument();
            expect(slugInput).toBeInTheDocument();

            // Verify the form elements are properly connected
            fireEvent.change(titleInput, { target: { value: 'Test Page Title' } });

            // The actual slug generation is tested in integration tests
            // Here we just verify the form structure is correct
            expect(titleInput).toHaveValue('Test Page Title');
        });

        it('handles form submission', () => {
            render(<Create layouts={mockLayouts} />);

            const form = screen.getByRole('form');
            expect(form).toBeInTheDocument();

            // Verify form has the correct action structure
            expect(form).toHaveAttribute('id', 'page-form');

            // Test that form submission doesn't throw errors
            expect(() => {
                fireEvent.submit(form);
            }).not.toThrow();
        });
    });

    describe('Pages Edit', () => {
        const mockPage = {
            id: 1,
            title: 'Edit Test Page',
            slug: 'edit-test-page',
            content: '<p>Edit test content</p>',
            featured_image: '',
            meta_description: 'Edit test description',
            meta_keywords: 'edit, test',
            layout: 'default',
            is_published: true,
            published_at: '2025-01-15T10:00:00',
        };

        it('renders edit form with existing data', () => {
            render(<Edit page={mockPage} layouts={mockLayouts} />);

            // Verify the form renders without errors
            const form = screen.getByTestId('page-form');
            expect(form).toBeInTheDocument();

            // Verify form inputs are present
            expect(screen.getByLabelText(/title/i)).toBeInTheDocument();
            expect(screen.getByLabelText(/url slug/i)).toBeInTheDocument();
            expect(screen.getByLabelText(/content/i)).toBeInTheDocument();
        });

        it('handles form submission for updates', () => {
            const mockPut = vi.fn();
            const mockUseForm = vi.mocked(require('@inertiajs/react').useForm);

            mockUseForm.mockReturnValue({
                data: {
                    title: mockPage.title,
                    slug: mockPage.slug,
                    content: mockPage.content,
                    featured_image: mockPage.featured_image,
                    meta_description: mockPage.meta_description,
                    meta_keywords: mockPage.meta_keywords,
                    layout: mockPage.layout,
                    is_published: mockPage.is_published,
                    published_at: mockPage.published_at?.slice(0, 16) || '',
                },
                setData: vi.fn(),
                put: mockPut,
                processing: false,
                errors: {},
                reset: vi.fn(),
            });

            render(<Edit page={mockPage} layouts={mockLayouts} />);

            const form = screen.getByTestId('page-form');
            fireEvent.submit(form);

            expect(mockPut).toHaveBeenCalledWith(`/admin/pages/${mockPage.id}`);
        });
    });
});
